#!/bin/bash

########## WARN #########
#
# THIS FILE IS NOT USED BY THE BUILD PIPELINE, INTENDED FOR LOCAL EXECUTION ONLY
#
########## WARN #########

set -eo pipefail

if [ -z "$1" ];
then
  echo "No STAGE argument supplied, defaulting to 'development'."
  STAGE=development
else
  STAGE="$1"
fi

set -eou pipefail

LIB_NAME=spl-rd-vio
TARGET_IMAGE="${LIB_NAME}:${STAGE}"

echo "Running ${TARGET_IMAGE}"

docker run \
  --entrypoint /bin/bash \
  --interactive \
  --name "${LIB_NAME}" \
  --network host \
  --rm \
  --tty \
  "${TARGET_IMAGE}"

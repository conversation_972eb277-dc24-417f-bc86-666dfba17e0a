#include "rdvio/sliding_window_tracker.h"

#include "rdvio/estimation/solver.h"
#include "rdvio/feature_tracker.h"
#include "rdvio/frontend.h"
#include "rdvio/geometry/pnp.h"
#include "rdvio/geometry/stereo.h"
#include "rdvio/map/frame.h"
#include "rdvio/map/map.h"
#include "rdvio/map/track.h"
#include "rdvio/sliding_window_tracker_utils.h"
#include "rdvio/types.h"

namespace rdvio {

void TrackLandmarks(const Frame& frame) {
#ifdef ENABLE_TIMING
  auto start = std::chrono::high_resolution_clock::now();
#endif

  for (size_t keypoint_idx{0}; keypoint_idx < frame.keypoint_num(); ++keypoint_idx) {
    if (Track* const track{frame.get_track(keypoint_idx)}; track) {
      if (not track->tag(TT_TRIANGULATED)) {
        if (const auto point{track->Triangulate()}; point.has_value()) {
          track->SetLandmarkPoint(point.value());
          track->tag(TT_TRIANGULATED) = true;
          track->tag(TT_VALID) = true;
          track->tag(TT_STATIC) = true;
        } else {
          // outlier
          track->landmark.inv_depth = -1.0;
          track->tag(TT_TRIANGULATED) = false;
          track->tag(TT_VALID) = false;
        }
      }
    }
  }

#ifdef ENABLE_TIMING
  auto end = std::chrono::high_resolution_clock::now();
  auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
  std::cout << "SlidingWindowTracker::track_landmark() : " << elapsed.count() << " ms" << std::endl;
#endif
}

SlidingWindowTracker::SlidingWindowTracker(std::unique_ptr<Map> keyframe_map, const SlidingWindowTrackerConfig& config,
                                           const std::shared_ptr<SystemStateMonitor> system_monitor)
    : map(std::move(keyframe_map)), config_(config) {
  tracker_component_monitor_ = system_monitor->RegisterComponent("SlidingWindowTracker");
  window_manager_monitor_ = system_monitor->RegisterComponent("SlidingWindowManagment");
  localizer_monitor_ = system_monitor->RegisterComponent("SWTLocalizer");

  for (size_t j = 1; j < map->frame_num(); ++j) {
    Frame* frame_i = map->get_frame(j - 1);
    Frame* frame_j = map->get_frame(j);
    frame_j->preintegration.integrate(frame_j->image->t, frame_i->motion.bias_gyroscope,
                                      frame_i->motion.bias_accelerometer, true, true);
  }
}

SlidingWindowTracker::~SlidingWindowTracker() = default;

void SlidingWindowTracker::MirrorFrame(Map* feature_tracking_map, size_t frame_id) {
  Frame* keyframe = map->get_frame(map->frame_num() - 1);
  Frame* new_frame_i = keyframe;
  if (!keyframe->subframes.empty()) {
    new_frame_i = keyframe->subframes.back().get();
  }

  size_t frame_index_i = feature_tracking_map->frame_index_by_id(new_frame_i->id());
  size_t frame_index_j = feature_tracking_map->frame_index_by_id(frame_id);

  if (frame_index_i == nil() || frame_index_j == nil()) {
    return;
  }

  Frame* old_frame_i = feature_tracking_map->get_frame(frame_index_i);
  Frame* old_frame_j = feature_tracking_map->get_frame(frame_index_j);

  std::unique_ptr<Frame> curr_frame = std::move(old_frame_j->clone());
  std::vector<ImuData>& new_data = curr_frame->preintegration.data;
  for (size_t index = frame_index_j - 1; index > frame_index_i; --index) {
    std::vector<ImuData> old_data = feature_tracking_map->get_frame(index)->preintegration.data;
    new_data.insert(new_data.begin(), old_data.begin(), old_data.end());
  }

  map->attach_frame(curr_frame->clone());
  Frame* new_frame_j = map->get_frame(map->frame_num() - 1);

  for (size_t ki = 0; ki < old_frame_i->keypoint_num(); ++ki) {
    if (Track* track = old_frame_i->get_track(ki)) {
      if (size_t kj = track->GetKeypointIndex(old_frame_j); kj != nil()) {
        Track* new_track = new_frame_i->get_track(ki, map.get());
        new_track->AddKeypoint(new_frame_j, kj);
        track->tag(TT_TRASH) = new_track->tag(TT_TRASH) && !new_track->tag(TT_STATIC);
      }
    }
  }

  map->prune_tracks([](const Track* track) { return track->tag(TT_TRASH) && !track->tag(TT_STATIC); });

  new_frame_j->preintegration.integrate(new_frame_j->image->t, new_frame_i->motion.bias_gyroscope,
                                        new_frame_i->motion.bias_accelerometer, true, true);
  new_frame_j->preintegration.predict(new_frame_i, new_frame_j);
}

bool SlidingWindowTracker::track() {
#ifdef ENABLE_TIMING
  auto start = std::chrono::high_resolution_clock::now();
#endif
  if (config_.parsac_flag) {
    if (JudgeTrackStatus()) {
      UpdateTrackStatus();
    }
  }

  LocalizeNewFrame();

  if (ManageKeyframe()) {
    const Frame* const new_frame{map->get_frame(map->frame_num() - 1)};
    TrackLandmarks(*new_frame);
    RefineWindow();
    SlideWindow();
  } else {
    RefineSubwindow();
  }
#ifdef ENABLE_TIMING
  auto end = std::chrono::high_resolution_clock::now();
  auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
  std::cout << "SlidingWindowTracker::Track(): " << elapsed.count() << " ms" << std::endl;
#endif
  return true;
}

void SlidingWindowTracker::LocalizeNewFrame() {
  const auto solver{Solver::Create()};

  Frame* frame_i = map->get_frame(map->frame_num() - 2);
  if (!frame_i->subframes.empty()) {
    frame_i = frame_i->subframes.back().get();
  }
  Frame* frame_j = map->get_frame(map->frame_num() - 1);

  solver->AddFrameStates(frame_j);

  solver->PutFactor(Solver::CreatePreintegrationPriorFactor(frame_i, frame_j, frame_j->preintegration));

  for (size_t k = 0; k < frame_j->keypoint_num(); ++k) {
    if (Track* track = frame_j->get_track(k)) {
      if (track->all_tagged(TT_VALID, TT_TRIANGULATED, TT_STATIC)) {
        solver->PutFactor(Solver::CreateReprojectionPriorFactor(frame_j, track));
      }
    }
  }

  const bool new_frame_succesful_localized{solver->solve()};
  if (not new_frame_succesful_localized) {
    localizer_monitor_->ReportState(ComponentState::CRITICAL, std::string("Frame could be not succesfully localized."));
  } else {
    localizer_monitor_->ReportState(ComponentState::OK);
  }
}

bool SlidingWindowTracker::ManageKeyframe() {
  Frame* keyframe_i = map->get_frame(map->frame_num() - 2);
  Frame* newframe_j = map->get_frame(map->frame_num() - 1);

  if (!keyframe_i->subframes.empty()) {
    if (keyframe_i->subframes.back()->tag(FT_NO_TRANSLATION)) {
      if (newframe_j->tag(FT_NO_TRANSLATION)) {
        // [T]...........<-[R]
        //  +-[R]-[R]-[R]
        // ==>
        // [T]
        //  +-[R-R]-[R]-[R]
      } else {
        // [T]...........<-[T]
        //  +-[R]-[R]-[R]
        // ==>
        // [T]........[R]-[T]
        //  +-[R]-[R]
        keyframe_i->subframes.back()->tag(FT_KEYFRAME) = true;
        map->attach_frame(std::move(keyframe_i->subframes.back()), map->frame_num() - 1);
        keyframe_i->subframes.pop_back();
        newframe_j->tag(FT_KEYFRAME) = true;
        return true;
      }
    } else {
      if (newframe_j->tag(FT_NO_TRANSLATION)) {
        // [T]...........<-[R]
        //  +-[T]-[T]-[T]
        // ==>
        // [T]........[T]
        //  +-[T]-[T]  +-[R]
        std::unique_ptr<Frame> frame_lifted = std::move(keyframe_i->subframes.back());
        keyframe_i->subframes.pop_back();
        frame_lifted->tag(FT_KEYFRAME) = true;
        frame_lifted->subframes.emplace_back(map->detach_frame(map->frame_num() - 1));
        map->attach_frame(std::move(frame_lifted));
        return true;
      } else {
        if (keyframe_i->subframes.size() >= config_.subframe_size) {
          // [T]...........<-[T]
          //  +-[T]-[T]-[T]
          // ==>
          // [T]............[T]
          //  +-[T]-[T]-[T]
          newframe_j->tag(FT_KEYFRAME) = true;
          return true;
        }
      }  // SlidingWindowTracker::RefineWindow()
    }
  }

  size_t mapped_landmark_count{0};
  for (size_t k = 0; k < newframe_j->keypoint_num(); ++k) {
    if (Track* track = newframe_j->get_track(k)) {
      if (track->all_tagged(TT_VALID, TT_TRIANGULATED, TT_STATIC)) {
        mapped_landmark_count++;
      }
    }
  }

  if (mapped_landmark_count < config_.min_mapped_landmark_count) {
    // If we have almost no Landmarks detected, this is siriously bad.
    tracker_component_monitor_->ReportState(ComponentState::CRITICAL, "Tracked Landmarks are not sufficient.");
  } else {
    tracker_component_monitor_->ReportState(ComponentState::OK);
  }

  bool is_keyframe = mapped_landmark_count < config_.force_keyframe_landmarks;

  if (is_keyframe) {
    newframe_j->tag(FT_KEYFRAME) = true;
    return true;
  } else {
    keyframe_i->subframes.emplace_back(map->detach_frame(map->frame_num() - 1));
    return false;
  }
}

void SlidingWindowTracker::RefineWindow() {
#ifdef ENABLE_TIMING
  auto start = std::chrono::high_resolution_clock::now();
#endif
  const auto solver{Solver::Create()};
  if (!map->marginalization_factor) {
    map->marginalization_factor = Solver::CreateMarginalizationFactor(map.get());
  }
  for (size_t i = 0; i < map->frame_num(); ++i) {
    Frame* frame = map->get_frame(i);
    solver->AddFrameStates(frame);
  }
  std::unordered_set<Track*> visited_tracks;
  for (size_t i = 0; i < map->frame_num(); ++i) {
    Frame* frame = map->get_frame(i);
    for (size_t j = 0; j < frame->keypoint_num(); ++j) {
      Track* track = frame->get_track(j);
      if (!track) {
        continue;
      }
      if (visited_tracks.count(track) > 0) {
        continue;
      }
      visited_tracks.insert(track);
      if (!track->tag(TT_VALID)) {
        continue;
      }
      if (!track->tag(TT_STATIC)) {
        continue;
      }
      if (!track->FirstFrame()->tag(FT_KEYFRAME)) {
        continue;
      }
      solver->AddTrackStates(track);
    }
  }

  solver->AddFactor(map->marginalization_factor.get());

  for (size_t i = 0; i < map->frame_num(); ++i) {
    Frame* frame = map->get_frame(i);
    for (size_t j = 0; j < frame->keypoint_num(); ++j) {
      Track* track = frame->get_track(j);
      if (!track) {
        continue;
      }
      if (!track->all_tagged(TT_VALID, TT_TRIANGULATED, TT_STATIC)) {
        continue;
      }
      if (!track->FirstFrame()->tag(FT_KEYFRAME)) {
        continue;
      }
      if (frame == track->FirstFrame()) {
        continue;
      }
      solver->AddFactor(frame->reprojection_error_factors[j].get());
    }
  }

  for (size_t j = 1; j < map->frame_num(); ++j) {
    Frame* frame_i = map->get_frame(j - 1);
    Frame* frame_j = map->get_frame(j);

    frame_j->keyframe_preintegration = frame_j->preintegration;
    if (!frame_i->subframes.empty()) {
      std::vector<ImuData> imu_data;
      for (size_t k = 0; k < frame_i->subframes.size(); ++k) {
        auto& sub_imu_data = frame_i->subframes[k]->preintegration.data;
        imu_data.insert(imu_data.end(), sub_imu_data.begin(), sub_imu_data.end());
      }
      frame_j->keyframe_preintegration.data.insert(frame_j->keyframe_preintegration.data.begin(), imu_data.begin(),
                                                   imu_data.end());
    }

    if (frame_j->keyframe_preintegration.integrate(frame_j->image->t, frame_i->motion.bias_gyroscope,
                                                   frame_i->motion.bias_accelerometer, true, true)) {
      solver->PutFactor(Solver::CreatePreintegrationErrorFactor(frame_i, frame_j, frame_j->keyframe_preintegration));
    }
  }

  solver->solve(true, true);

  for (size_t k = 0; k < map->track_num(); ++k) {
    Track* track = map->get_track(k);
    if (track->tag(TT_TRIANGULATED)) {
      bool is_valid = true;
      auto x = track->GetLandmarkPoint();
      double rpe = 0.0;
      double rpe_count = 0.0;
      for (const auto& [frame, keypoint_index] : track->KeypointMap()) {
        if (!frame->tag(FT_KEYFRAME)) {
          continue;
        }
        PoseState pose = frame->get_pose(frame->camera);
        vector<3> y = pose.q.conjugate() * (x - pose.p);
        if (y.z() <= 1.0e-3 || y.z() > config_.max_feature_height) {  // todo
          is_valid = false;
          break;
        }
        rpe += (PinholeProject(y, frame->K) - PinholeProject(frame->GetBearing(keypoint_index), frame->K)).norm();
        rpe_count += 1.0;
      }
      is_valid = is_valid && (rpe / std::max(rpe_count, 1.0) < 3.0);
      track->tag(TT_VALID) = is_valid;
    } else {
      track->landmark.inv_depth = -1.0;
    }
  }

  for (size_t k = 0; k < map->track_num(); ++k) {
    Track* track = map->get_track(k);
    if (!track->tag(TT_VALID)) {
      track->tag(TT_TRASH) = true;
    }
  }

#ifdef ENABLE_TIMING
  auto end = std::chrono::high_resolution_clock::now();
  auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
  std::cout << "SlidingWindowTracker::RefineWindow(): " << elapsed.count() << " ms" << std::endl;
#endif
}

void SlidingWindowTracker::SlideWindow() {
  while (map->frame_num() > config_.window_size) {
    Frame* frame = map->get_frame(0);
    for (size_t i = 0; i < frame->subframes.size(); ++i) {
      map->untrack_frame(frame->subframes[i].get());
    }
    map->marginalize_frame();
  }
}

void SlidingWindowTracker::RefineSubwindow() {
  Frame* frame = map->get_frame(map->frame_num() - 1);
  if (frame->subframes.empty()) {
    return;
  }

  // ToDo(vkallenbach): Remove / refactro the duplicated code here.
  const auto solver{Solver::Create()};
  if (frame->subframes[0]->tag(FT_NO_TRANSLATION)) {
    if (frame->subframes.size() >= 9) {
      for (size_t i = frame->subframes.size() / 3; i > 0; --i) {
        Frame* tgt_frame = frame->subframes[i * 3 - 1].get();
        std::vector<ImuData> imu_data;
        for (size_t j = i * 3 - 1; j > (i - 1) * 3; --j) {
          Frame* src_frame = frame->subframes[j - 1].get();
          imu_data.insert(imu_data.begin(), src_frame->preintegration.data.begin(),
                          src_frame->preintegration.data.end());
          map->untrack_frame(src_frame);
          frame->subframes.erase(frame->subframes.begin() + (j - 1));
        }
        tgt_frame->preintegration.data.insert(tgt_frame->preintegration.data.begin(), imu_data.begin(), imu_data.end());
      }
    }

    frame->tag(FT_FIX_POSE) = true;
    frame->tag(FT_FIX_MOTION) = true;

    solver->AddFrameStates(frame);

    for (size_t i = 0; i < frame->subframes.size(); ++i) {
      Frame* subframe = frame->subframes[i].get();
      solver->AddFrameStates(subframe);
      Frame* prev_frame = (i == 0 ? frame : frame->subframes[i - 1].get());
      subframe->preintegration.integrate(subframe->image->t, prev_frame->motion.bias_gyroscope,
                                         prev_frame->motion.bias_accelerometer, true, true);
      solver->PutFactor(Solver::CreatePreintegrationErrorFactor(prev_frame, subframe, subframe->preintegration));
    }

    Frame* last_subframe = frame->subframes.back().get();
    for (size_t k = 0; k < last_subframe->keypoint_num(); ++k) {
      if (Track* track = last_subframe->get_track(k)) {
        if (track->tag(TT_VALID)) {
          if (track->tag(TT_TRIANGULATED)) {
            if (track->tag(TT_STATIC)) {
              solver->PutFactor(Solver::CreateReprojectionPriorFactor(last_subframe, track));
            }
          } else {
            solver->PutFactor(Solver::CreateRotationPriorFactor(last_subframe, track));
          }
        }
      }
    }
  } else {
    frame->tag(FT_FIX_POSE) = true;
    frame->tag(FT_FIX_MOTION) = true;

    solver->AddFrameStates(frame);

    for (size_t i = 0; i < frame->subframes.size(); ++i) {
      Frame* subframe = frame->subframes[i].get();
      solver->AddFrameStates(subframe);
      Frame* prev_frame = (i == 0 ? frame : frame->subframes[i - 1].get());
      subframe->preintegration.integrate(subframe->image->t, prev_frame->motion.bias_gyroscope,
                                         prev_frame->motion.bias_accelerometer, true, true);
      solver->PutFactor(Solver::CreatePreintegrationErrorFactor(prev_frame, subframe, subframe->preintegration));
      for (size_t k = 0; k < subframe->keypoint_num(); ++k) {
        if (Track* track = subframe->get_track(k)) {
          if (track->all_tagged(TT_VALID, TT_TRIANGULATED, TT_STATIC)) {
            if (track->FirstFrame()->tag(FT_KEYFRAME)) {
              solver->PutFactor(Solver::CreateReprojectionPriorFactor(subframe, track));
            } else if (track->FirstFrame()->id() > frame->id()) {
              solver->AddFactor(frame->reprojection_error_factors[k].get());
            }
          }
        }
      }
    }
  }

  frame->tag(FT_FIX_POSE) = false;
  frame->tag(FT_FIX_MOTION) = false;

  const bool window_refinement_succesful{solver->solve()};

  if (not window_refinement_succesful) {
    window_manager_monitor_->ReportState(ComponentState::CRITICAL, std::string("Window refinement failed."));
  } else {
    window_manager_monitor_->ReportState(ComponentState::OK);
  }
}  // namespace rdvio

KinematicState SlidingWindowTracker::GetLatestState() const {
  const Frame* frame = map->get_frame(map->frame_num() - 1);
  if (!frame->subframes.empty()) {
    frame = frame->subframes.back().get();
  }
  return {frame->image->t, frame->pose, frame->motion};
}

double compute_epipolar_dist(matrix<3> F, vector<2>& pt1, vector<2>& pt2) {
  vector<3> l = F * pt1.homogeneous();
  double dist = std::abs(pt2.homogeneous().transpose() * l) / l.segment<2>(0).norm();
  return dist;
}

bool SlidingWindowTracker::FilterParsac2d2d(Frame* frame_i, Frame* frame_j, std::vector<char>& mask,
                                            std::vector<size_t>& pts_to_index) {
  std::vector<vector<2>> pts1, pts2;

  for (size_t ki = 0; ki < frame_i->keypoint_num(); ++ki) {
    if (Track* track = frame_i->get_track(ki)) {
      if (size_t kj = track->GetKeypointIndex(frame_j)) {
        if (kj != nil()) {
          pts1.push_back(frame_i->GetBearing(ki).hnormalized());
          pts2.push_back(frame_j->GetBearing(kj).hnormalized());
          pts_to_index.push_back(kj);
        }
      }
    }
  }

  if (pts1.size() < 10) {
    return false;
  }

  matrix<3> E = find_essential_matrix_parsac(pts1, pts2, mask, m_th / frame_i->K(0, 0));

  return true;
}

bool SlidingWindowTracker::JudgeTrackStatus() {
  Frame* curr_frame = map->get_frame(map->frame_num() - 1);
  Frame* keyframe = map->get_frame(map->frame_num() - 2);
  Frame* last_frame = keyframe;
  if (!keyframe->subframes.empty()) {
    last_frame = keyframe->subframes.back().get();
  }

  curr_frame->preintegration.integrate(curr_frame->image->t, last_frame->motion.bias_gyroscope,
                                       last_frame->motion.bias_accelerometer, true, true);
  curr_frame->preintegration.predict(last_frame, curr_frame);

  m_P2D.clear();
  m_P3D.clear();
  m_lens.clear();
  m_indices_map = std::vector<int>(curr_frame->keypoint_num(), -1);

  for (size_t k = 0; k < curr_frame->keypoint_num(); ++k) {
    if (Track* const track = curr_frame->get_track(k); track) {
      if (track->all_tagged(TT_VALID, TT_TRIANGULATED)) {
        const vector<3>& bearing = curr_frame->GetBearing(k);
        const vector<3>& landmark = track->GetLandmarkPoint();
        m_P2D.push_back(bearing.hnormalized());
        m_P3D.push_back(landmark);
        m_lens.push_back(std::max(track->m_life, size_t(0)));
        m_indices_map[k] = m_P3D.size() - 1;
      }
    }
  }

  if (m_P2D.size() < 20) {
    return false;
  }

  const PoseState& pose = curr_frame->get_pose(curr_frame->camera);

  std::vector<char> mask;
  matrix<3> Rcw = pose.q.inverse().toRotationMatrix();
  vector<3> tcw = pose.q.inverse() * pose.p * (-1.0);
  matrix<4> T_IMU =
      find_pnp_matrix_parsac_imu(m_P3D, m_P2D, m_lens, Rcw, tcw, 0.20, 1.0, mask, 1.0 / curr_frame->K(0, 0));

  const Pose imu_camera_pose_delta = ImuCameraPoseDelta(keyframe, curr_frame);

  // NOTE(Jack): I believe that the following section of code is left over debugging code from the original development.
  // All the code in between the following brackets can be removed, I think (!). Please check for yourself that it is
  // read only and there are no side effects in this sub namespace.
  // check rpe
  {
    std::vector<vector<2>> P2D_inliers, P2D_outliers;
    std::vector<vector<3>> P3D_inliers, P3D_outliers;

    for (int i = 0; i < m_P2D.size(); ++i) {
      if (mask[i]) {
        P2D_inliers.push_back(m_P2D[i]);
        P3D_inliers.push_back(m_P3D[i]);
      } else {
        P2D_outliers.push_back(m_P2D[i]);
        P3D_outliers.push_back(m_P3D[i]);
      }
    }

    std::vector<double> inlier_errs, outlier_errs;
    // cppcheck-suppress unreadVariable
    double inlier_errs_sum = 0;
    // cppcheck-suppress unreadVariable
    double outlier_errs_sum = 0;
    for (int i = 0; i < P2D_inliers.size(); i++) {
      vector<3> p = pose.q.conjugate() * (P3D_inliers[i] - pose.p);
      double proj_err =
          (PinholeProject(p, curr_frame->K) - PinholeProject(P2D_inliers[i].homogeneous(), curr_frame->K)).norm();
      inlier_errs.push_back(proj_err);
      // cppcheck-suppress unreadVariable
      inlier_errs_sum += proj_err;
    }

    for (int i = 0; i < P2D_outliers.size(); i++) {
      vector<3> p = pose.q.conjugate() * (P3D_outliers[i] - pose.p);
      double proj_err =
          (PinholeProject(p, curr_frame->K) - PinholeProject(P2D_outliers[i].homogeneous(), curr_frame->K)).norm();
      outlier_errs.push_back(proj_err);
      // cppcheck-suppress unreadVariable
      outlier_errs_sum += proj_err;
    }
  }

  matrix<3> E = compute_essential_matrix(imu_camera_pose_delta);
  matrix<3> F = keyframe->K.transpose().inverse() * E * curr_frame->K.inverse();

  std::vector<vector<2>> inlier_set1, inlier_set2;
  std::vector<vector<2>> outlier_set1, outlier_set2;
  for (size_t i = 0; i < curr_frame->keypoint_num(); ++i) {
    if (m_indices_map[i] != -1) {
      if (size_t j = curr_frame->get_track(i)->GetKeypointIndex(keyframe); j != nil()) {
        if (mask[m_indices_map[i]]) {
          inlier_set1.push_back(PinholeProject(keyframe->GetBearing(j), keyframe->K));
          inlier_set2.push_back(PinholeProject(curr_frame->GetBearing(i), curr_frame->K));
        } else {
          outlier_set1.push_back(PinholeProject(keyframe->GetBearing(j), keyframe->K));
          outlier_set2.push_back(PinholeProject(curr_frame->GetBearing(i), curr_frame->K));
        }
      }
    }
  }

  std::vector<double> inliers_dist, outliers_dist;

  for (int i = 0; i < inlier_set1.size(); i++) {
    vector<2>& p1 = inlier_set1[i];
    vector<2>& p2 = inlier_set2[i];
    double err = compute_epipolar_dist(F, p1, p2) + compute_epipolar_dist(F.transpose(), p2, p1);
    inliers_dist.push_back(err);
  }

  for (int i = 0; i < outlier_set1.size(); i++) {
    vector<2>& p1 = outlier_set1[i];
    vector<2>& p2 = outlier_set2[i];
    double err = compute_epipolar_dist(F, p1, p2) + compute_epipolar_dist(F.transpose(), p2, p1);
    outliers_dist.push_back(err);
  }

  size_t min_num = 20;
  if (inliers_dist.size() < min_num || outliers_dist.size() < min_num) {
    return false;
  }

  std::sort(inliers_dist.begin(), inliers_dist.end());
  std::sort(outliers_dist.begin(), outliers_dist.end());

  double th1 = inliers_dist[size_t(inliers_dist.size() * 0.5)];
  double th2 = outliers_dist[size_t(outliers_dist.size() * 0.5)];

  if (th2 < th1 * 2) {  // mean there is ambiguity
    return false;
  }

  m_th = (th1 + th2) / 2;

  for (size_t k = 0; k < curr_frame->keypoint_num(); ++k) {
    if (const Track* const track = curr_frame->get_track(k); track) {
      if (m_indices_map[k] != -1) {
        if (mask[m_indices_map[k]]) {
          curr_frame->get_track(k)->tag(TT_OUTLIER) = false;
          curr_frame->get_track(k)->tag(TT_STATIC) = true;
        } else {
          curr_frame->get_track(k)->tag(TT_OUTLIER) = true;
          curr_frame->get_track(k)->tag(TT_STATIC) = false;
        }
      }
    }
  }

  return true;
}

void SlidingWindowTracker::UpdateTrackStatus() {
  Frame* curr_frame = map->get_frame(map->frame_num() - 1);
  size_t frame_id = feature_tracking_map->frame_index_by_id(curr_frame->id());

  if (frame_id == nil()) {
    return;
  }

  Frame* old_frame = feature_tracking_map->get_frame(frame_id);

  std::vector<size_t> outlier_cnts(curr_frame->keypoint_num(), 0);
  std::vector<size_t> matches_cnts(curr_frame->keypoint_num(), 0);
  size_t start_idx =
      std::min(map->frame_num() - 1, std::max(map->frame_num() - 1 - config_.parsac_keyframe_check_size, size_t(0)));
  for (size_t i = start_idx; i < map->frame_num() - 1; i++) {
    std::vector<char> mask;
    std::vector<size_t> pts_to_index;
    if (FilterParsac2d2d(map->get_frame(i), curr_frame, mask, pts_to_index)) {
      for (size_t j = 0; j < mask.size(); j++) {
        if (!mask[j]) {
          outlier_cnts[pts_to_index[j]] += 1;
        }
        matches_cnts[pts_to_index[j]] += 1;
      }
    }
  }

  for (size_t i = 0; i < curr_frame->keypoint_num(); i++) {
    if (Track* curr_track = curr_frame->get_track(i)) {
      if (size_t j = curr_track->GetKeypointIndex(old_frame)) {
        if (j != nil()) {
          Track* old_track = old_frame->get_track(j);
          size_t outlier_th = map->frame_num() / 2;
          if (outlier_cnts[i] > outlier_th / 2 && outlier_cnts[i] > 0.8 * matches_cnts[i]) {
            curr_track->tag(TT_STATIC) = false;
          }
          if (!old_track->tag(TT_STATIC) || !curr_track->tag(TT_STATIC)) {
            curr_track->tag(TT_STATIC) = false;
            old_track->tag(TT_STATIC) = false;
          }
        }
      }
    }
  }
}

std::vector<Eigen::Vector3d> SlidingWindowTracker::GetTriangulatedLandmarks() const {
  std::vector<Eigen::Vector3d> points;
  points.reserve(map->track_num());
  for (auto i = 0; i < map->track_num(); ++i) {
    if (Track* track = map->get_track(i)) {
      if (track->all_tagged(TT_VALID, TT_TRIANGULATED)) {
        points.push_back(track->GetLandmarkPoint());
      }
    }
  }
  return points;
}

std::vector<size_t> SlidingWindowTracker::GetLandmarksTrackingCount() const {
  std::vector<size_t> track_length;
  track_length.reserve(map->track_num());
  for (auto i = 0; i < map->track_num(); ++i) {
    if (Track* track = map->get_track(i)) {
      if (track->all_tagged(TT_VALID, TT_TRIANGULATED)) {
        track_length.push_back(track->TrackLength());
      }
    }
  }
  return track_length;
}

}  // namespace rdvio

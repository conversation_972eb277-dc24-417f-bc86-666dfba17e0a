#include "rdvio/config.hpp"

#include "rdvio/types.h"
#include "rdvio/util/yaml.hpp"

namespace rdvio {

Config::Config(const std::string& device_config_filename, const std::optional<std::string> slam_config_filename)
    : Config(utils::LoadFile(device_config_filename),
             slam_config_filename.has_value() ? std::optional<YAML::Node>(utils::LoadFile(slam_config_filename.value()))
                                              : std::nullopt) {}

Config::Config(const YAML::Node& device_config, const std::optional<YAML::Node> slam_config) {
  if (slam_config.has_value()) {
    if (slam_config.value()["feature_tracker"]) {
      feature_tracker_config_ = FeatureTrackerConfig(slam_config.value()["feature_tracker"]);
    }

    if (slam_config.value()["solver"]) {
      solver_config_ = SolverConfig(slam_config.value()["solver"]);
    }

    if (slam_config.value()["initializer"]) {
      initializer_config_ = InitializerConfig(slam_config.value()["initializer"]);
    }

    if (slam_config.value()["sliding_window"]) {
      sliding_window_tracker_config_ = SlidingWindowTrackerConfig(slam_config.value()["sliding_window"]);
    }
  }

  camera_config_ = CameraConfig(device_config["cam0"]);
  imu_config_ = ImuConfig(device_config["imu"]);
}

SolverConfig::SolverConfig(const YAML::Node& config) {
  utils::AssignValue(config, "num_threads", false, num_threads);

  utils::AssignValue(config, "max_num_iterations", false, max_num_iterations);

  utils::AssignValue(config, "max_solver_time_in_seconds", false, max_solver_time_in_seconds);

  utils::AssignValue(config, "function_tolerance", false, function_tolerance);
}

FeatureTrackerConfig::FeatureTrackerConfig(const YAML::Node& config) {
  if (config["clahe"]) {
    clahe_config = ClaheConfig(config["clahe"]);
  }
  if (config["optical_flow"]) {
    optical_flow_config = OpticalFlowConfig(config["optical_flow"]);
  }

  utils::AssignValue(config, "min_keypoint_distance", false, min_keypoint_distance);

  utils::AssignValue(config, "max_keypoint_detection", false, max_keypoint_detection);

  utils::AssignValue(config, "max_init_frames", false, max_init_frames);

  utils::AssignValue(config, "max_frames", false, max_frames);

  utils::AssignValue(config, "predict_keypoints", false, predict_keypoints);

  utils::AssignValue(config, "rotation_misalignment_threshold", false, rotation_misalignment_threshold);

  utils::AssignValue(config, "angle_percentile", false, angle_percentile);

  utils::AssignValue(config, "rotation_ransac_threshold", false, rotation_ransac_threshold);

  utils::AssignValue(config, "sliding_window_tracker_frequent", false, sliding_window_tracker_frequent);
}

OpticalFlowConfig::OpticalFlowConfig(const YAML::Node& config) {
  utils::AssignValue(config, "level_num", false, level_num);

  utils::AssignValue(config, "max_iterations", false, max_iterations);

  utils::AssignValue(config, "epsilon", false, epsilon);

  utils::AssignValue(config, "window_size", false, window_size);

  utils::AssignValue(config, "max_movement_factor", false, max_movement_factor);

  utils::AssignValue(config, "cross_check_error_th", false, cross_check_error_th);

  utils::AssignValue(config, "cross_check_error_rate_warning_threshold", false,
                     cross_check_error_rate_warning_threshold);

  utils::AssignValue(config, "reuse_input_image", false, reuse_input_image);

  utils::AssignValue(config, "draw_optical_flow", false, draw_optical_flow);

  const bool is_output_filepath_required{draw_optical_flow};
  utils::AssignValue(config, "optical_flow_output_path", is_output_filepath_required, optical_flow_output_path);
}

ClaheConfig::ClaheConfig(const YAML::Node& config) {
  utils::AssignValue(config, "clip_limit", false, clip_limit);

  utils::AssignValue(config, "width", false, width);

  utils::AssignValue(config, "height", false, height);
}

InitializerConfig::InitializerConfig(const YAML::Node& config) {
  if (config["sfm"]) {
    sfm_initializer_config = SfmInitializerConfig(config["sfm"]);
  }

  utils::AssignValue(config, "keyframe_num", false, keyframe_num);

  utils::AssignValue(config, "keyframe_gap", false, keyframe_gap);

  utils::AssignValue(config, "min_landmarks", false, min_landmarks);

  utils::AssignValue(config, "refine_imu", false, refine_imu);
}

SfmInitializerConfig::SfmInitializerConfig(const YAML::Node& config) {
  utils::AssignValue(config, "min_matches", false, min_matches);

  utils::AssignValue(config, "min_parallax", false, min_parallax);

  utils::AssignValue(config, "min_triangulation", false, min_triangulation);

  utils::AssignValue(config, "random", false, random);
}

SlidingWindowTrackerConfig::SlidingWindowTrackerConfig(const YAML::Node& config) {
  utils::AssignValue(config, "sliding_window_size", false, window_size);

  utils::AssignValue(config, "subframe_size", false, subframe_size);

  utils::AssignValue(config, "force_keyframe_landmarks", false, force_keyframe_landmarks);

  utils::AssignValue(config, "max_feature_height", false, max_feature_height);

  utils::AssignValue(config, "parsac_flag", false, parsac_flag);

  utils::AssignValue(config, "parsac_keyframe_check_size", false, parsac_keyframe_check_size);
}

CameraConfig::CameraConfig(const YAML::Node& config) {
  intrinsics = spl::camera_models::IntrinsicConfigFromKalibr(config);

  K = spl::camera_models::MakeK(intrinsics.pinhole_intrinsics).cast<double>();

  utils::AssignValue(config, "do_image_rectification", true, do_image_rectification);

  utils::AssignMatrix(config, "noise", true, keypoint_noise_cov);

  utils::AssignValue(config, "camera_tracking_margin", true, camera_tracking_margin);

  utils::AssignVector(config, "extrinsic.q_bc", true, camera_to_body_rotation.coeffs());

  utils::AssignVector(config, "extrinsic.p_bc", true, camera_to_body_translation);
}

ImuConfig::ImuConfig(const YAML::Node& config) {
  // NOTE (Vipul): The Body to IMU pose always have been Identity so far and therefore, we have never tested the
  // scenario where it is not identity. Until we have such a scenario, we will not expose the option to set the IMU
  // extrinsics via the config file.
  imu_to_body_rotation = quaternion::Identity();
  imu_to_body_translation = vector<3>::Zero();

  double gyroscope_noise_density;
  utils::AssignValue(config, "gyroscope_noise_density", true, gyroscope_noise_density);
  if (gyroscope_noise_density < 0) {
    throw std::runtime_error("gyroscope_noise_density must be positive.");
  }
  gyroscope_noise_cov = Eigen::MatrixXd::Identity(3, 3) * std::pow(gyroscope_noise_density, 2);

  double gyroscope_random_walk;
  utils::AssignValue(config, "gyroscope_random_walk", true, gyroscope_random_walk);
  if (gyroscope_random_walk < 0) {
    throw std::runtime_error("gyroscope_random_walk must be positive.");
  }
  gyroscope_bias_noise_cov = Eigen::MatrixXd::Identity(3, 3) * std::pow(gyroscope_random_walk, 2);

  double accelerometer_noise_density;
  utils::AssignValue(config, "accelerometer_noise_density", true, accelerometer_noise_density);
  if (accelerometer_noise_density < 0) {
    throw std::runtime_error("accelerometer_noise_density must be positive.");
  }
  accelerometer_noise_cov = Eigen::MatrixXd::Identity(3, 3) * std::pow(accelerometer_noise_density, 2);

  double accelerometer_random_walk;
  utils::AssignValue(config, "accelerometer_random_walk", true, accelerometer_random_walk);
  if (accelerometer_random_walk < 0) {
    throw std::runtime_error("accelerometer_random_walk must be positive.");
  }
  accelerometer_bias_noise_cov = Eigen::MatrixXd::Identity(3, 3) * std::pow(accelerometer_random_walk, 2);
}

}  // namespace rdvio

#pragma once

#include "rdvio/config.hpp"
#include "rdvio/estimation/state.h"
#include "rdvio/system_state_monitor.h"
#include "rdvio/types.h"

namespace rdvio {

class Frame;
class Map;

void TrackLandmarks(const Frame& frame);

class SlidingWindowTracker {
 public:
  SlidingWindowTracker(std::unique_ptr<Map> keyframe_map, const SlidingWindowTrackerConfig& config,
                       const std::shared_ptr<SystemStateMonitor> system_monitor);

  ~SlidingWindowTracker();

  void MirrorFrame(Map* feature_tracking_map, size_t frame_id);

  bool track();

  std::vector<Eigen::Vector3d> GetTriangulatedLandmarks() const;

  std::vector<size_t> GetLandmarksTrackingCount() const;

  KinematicState GetLatestState() const;

  std::shared_ptr<Map> feature_tracking_map;

 private:
  void LocalizeNewFrame();

  void RefineWindow();

  void SlideWindow();

  bool ManageKeyframe();

  void RefineSubwindow();

  bool JudgeTrackStatus();

  bool FilterParsac2d2d(Frame* frame_i, Frame* frame_j, std::vector<char>& mask, std::vector<size_t>& pts_to_index);

  void UpdateTrackStatus();

  std::shared_ptr<ComponentHandle> tracker_component_monitor_;
  std::shared_ptr<ComponentHandle> window_manager_monitor_;
  std::shared_ptr<ComponentHandle> localizer_monitor_;

  const SlidingWindowTrackerConfig config_;

  std::unique_ptr<Map> map;

  std::vector<vector<3>> m_P3D;
  std::vector<vector<2>> m_P2D;
  std::vector<size_t> m_lens;
  std::vector<int> m_indices_map;

  double m_th;
};

}  // namespace rdvio

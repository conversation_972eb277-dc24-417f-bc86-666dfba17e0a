#pragma once

#include <iostream>
#include <string>
#include <vector>

#include <spl-camera-models/intrinsic_config.hpp>
#include <yaml-cpp/yaml.h>

#include "rdvio/types.h"

namespace rdvio {

struct ClaheConfig {
 public:
  ClaheConfig() {};

  ClaheConfig(const YAML::Node& config);

  double clip_limit{6.0};
  size_t width{8};
  size_t height{8};
};

struct OpticalFlowConfig {
 public:
  OpticalFlowConfig() {};

  OpticalFlowConfig(const YAML::Node& config);

  size_t level_num{3};
  size_t max_iterations{30};
  double epsilon{0.01};
  size_t window_size{21};
  double max_movement_factor{0.25};
  double cross_check_error_th{0.5};

  // Percentage threshold for warning when cross-check error rate is too high */
  double cross_check_error_rate_warning_threshold{25.0};
  bool reuse_input_image{true};
  bool draw_optical_flow{false};
  std::string optical_flow_output_path{""};
};

struct FeatureTrackerConfig {
 public:
  FeatureTrackerConfig() {};

  FeatureTrackerConfig(const YAML::Node& config);

  /**
   * @brief In pixels.
   */
  double min_keypoint_distance{20.0};
  size_t max_keypoint_detection{150};
  size_t max_init_frames{60};
  size_t max_frames{200};
  bool predict_keypoints{true};
  double rotation_misalignment_threshold{0.1};

  /**
   * @brief Percentile for rotation misalignment calculation.
   *
   * Compute angles between predicted and actual feature bearings, sort them,
   * and use the percentile angle to determine if the motion is purely rotational.
   */
  double angle_percentile{0.7};

  /**
   * @brief In Degrees.
   */
  double rotation_ransac_threshold{10};
  size_t sliding_window_tracker_frequent{1};
  ClaheConfig clahe_config;
  OpticalFlowConfig optical_flow_config;
};

struct SolverConfig {
 public:
  SolverConfig() {};
  SolverConfig(const YAML::Node& config);

  size_t num_threads{1};
  size_t max_num_iterations{30};
  double max_solver_time_in_seconds{0.2};
  double function_tolerance{0.01};
};

struct SfmInitializerConfig {
 public:
  SfmInitializerConfig() {};
  SfmInitializerConfig(const YAML::Node& config);

  size_t min_matches{50};
  double min_parallax{10};
  size_t min_triangulation{50};
  size_t random{648};
};

struct InitializerConfig {
 public:
  InitializerConfig() {};
  InitializerConfig(const YAML::Node& config);

  size_t keyframe_num{8};
  size_t keyframe_gap{5};
  size_t min_landmarks{30};
  bool refine_imu{true};
  SfmInitializerConfig sfm_initializer_config;
};

struct SlidingWindowTrackerConfig {
 public:
  SlidingWindowTrackerConfig() {};

  SlidingWindowTrackerConfig(const YAML::Node& config);
  /*
   * @brief: If set to >0 we will report critical function if the amount is less
   *         then specified here (Usually this indicates divergence).
   */
  size_t min_mapped_landmark_count{0};
  size_t window_size{10};
  size_t subframe_size{3};
  size_t force_keyframe_landmarks{35};

  /**
   * @brief Maximum allowed height (meters) (z component) of the keypoint in the track.
   */
  double max_feature_height{50};
  bool parsac_flag{false};
  size_t parsac_keyframe_check_size{3};
};

struct CameraConfig {
 public:
  CameraConfig() {};
  CameraConfig(const YAML::Node& config);

  // TODO (Vipul): The camera_to_body_rotation gives an impression that this might a rotation matrix. Rename it to
  // avoid the ambiguity.
  quaternion camera_to_body_rotation;
  vector<3> camera_to_body_translation;

  spl::camera_models::IntrinsicConfig intrinsics;
  matrix<3> K;

  bool do_image_rectification;
  matrix<2> keypoint_noise_cov;
  size_t camera_tracking_margin;
};

struct ImuConfig {
 public:
  ImuConfig() {};

  ImuConfig(const YAML::Node& config);

  /**
   * @brief The rotation of the Imu frame w.r.t the body frame represented as a normalized quaternion. The quaternion
   * must follow [x, y, z, w]
   *
   * @todo (Vipul): The imu_to_body_rotation gives an impression that this might a rotation matrix. Rename it to avoid
   * the ambiguity.
   */
  quaternion imu_to_body_rotation;

  /**
   * @brief The translation of the Imu frame w.r.t the body frame represented as a 3D Cartesian vector. The translation
   * must follow [x, y, z] order
   */
  vector<3> imu_to_body_translation;

  /**
   * @brief Gyroscope noise covariance matrix. It is the (gyroscope_noise_density)^2 on the diagonal where
   * gyroscope_noise_density has the unit [ rad / s / sqrt(Hz) ].
   */
  matrix<3> gyroscope_noise_cov;

  /**
   * @brief Accelerometer noise covariance matrix. It is the (accelerometer_noise_density)^2 on the diagonal where
   * accelerometer_noise_density has the unit [ m / s^2 / sqrt(Hz) ].
   */
  matrix<3> accelerometer_noise_cov;

  /**
   * @brief Gyroscope bias noise covariance matrix. It is the (gyroscope_random_walk)^2 on the diagonal where
   * gyroscope_random_walk has the unit [ rad / s^2 / sqrt(Hz) ].
   */
  matrix<3> gyroscope_bias_noise_cov;

  /**
   * @brief Accelerometer bias noise covariance matrix. It is the (accelerometer_random_walk)^2 on the diagonal where
   * accelerometer_random_walk has the unit [ m / s^3 / sqrt(Hz) ].
   */
  matrix<3> accelerometer_bias_noise_cov;
};

class Config {
 public:
  Config(const std::string& device_config_filename,
         const std::optional<std::string> slam_config_filename = std::nullopt);

  Config(const YAML::Node& device_config, const std::optional<YAML::Node> slam_config = std::nullopt);

  FeatureTrackerConfig GetFeatureTrackerConfig() const { return feature_tracker_config_; };

  SolverConfig GetSolverConfig() const { return solver_config_; };

  InitializerConfig GetInitializerConfig() const { return initializer_config_; };

  SlidingWindowTrackerConfig GetSlidingWindowTrackerConfig() const { return sliding_window_tracker_config_; };

  CameraConfig GetCameraConfig() const { return camera_config_; };

  ImuConfig GetImuConfig() const { return imu_config_; };

 private:
  FeatureTrackerConfig feature_tracker_config_;
  SolverConfig solver_config_;
  InitializerConfig initializer_config_;
  SlidingWindowTrackerConfig sliding_window_tracker_config_;
  CameraConfig camera_config_;
  ImuConfig imu_config_;
};

}  // namespace rdvio
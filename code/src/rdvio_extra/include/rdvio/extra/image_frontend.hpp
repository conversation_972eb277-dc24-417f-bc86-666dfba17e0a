#pragma once

#include <iostream>

#include <opencv2/opencv.hpp>

#include "rdvio/config.hpp"
#include "rdvio/types.h"

namespace rdvio::extra {

class ImageFrontend {
 public:
  double t;

  ImageFrontend(const cv::Mat& image, const double timestamp, const bool enable_timing = false);

  virtual ~ImageFrontend() = default;

  size_t GetWidth() const;

  size_t GetHeight() const;

  void PreProcess(const ClaheConfig& config);

  void DetectKeypoints(const size_t max_points, const double keypoint_distance, std::vector<vector<2>>& keypoints);

  void TrackKeypoints(const ImageFrontend* const next_image, const std::vector<vector<2>>& curr_keypoints,
                      std::vector<vector<2>>& next_keypoints, std::vector<char>& result_status);

  void ReleaseImageBuffer();

 protected:
  virtual void PreProcessImpl(const ClaheConfig& config) = 0;

  virtual void DetectKeypointsImpl(const size_t max_points, const double keypoint_distance,
                                   std::vector<vector<2>>& keypoints) const = 0;

  virtual void TrackKeypointsImpl(const ImageFrontend* const next_image, const std::vector<vector<2>>& curr_keypoints,
                                  std::vector<vector<2>>& next_keypoints, std::vector<char>& result_status) const = 0;

  virtual void ReleaseImageBufferImpl() = 0;

  cv::Mat image_;

 private:
  bool enable_timing_;
};

}  // namespace rdvio::extra
#pragma once

#include <string>
#include <vector>

#include <opencv2/opencv.hpp>

namespace rdvio::extra {

/**
 * @brief Visualizes optical flow tracking results by drawing keypoints and saving the output image.
 *
 * @param cur_image_gray The current frame as a grayscale image
 * @param next_image_gray The next frame as a grayscale image
 * @param next_timestamp Timestamp of the next frame, used for filename generation
 * @param undistorted_curr_cv_points Vector of keypoint positions in the current frame (undistorted coordinates)
 * @param undistorted_next_cv_points Vector of tracked keypoint positions in the next frame (undistorted coordinates)
 * @param predicted_cv_points Vector of predicted keypoint positions based on motion model (IMU)
 * @param result_status Vector indicating tracking success for each keypoint (1 = success, 0 = failure)
 * @param output_path Directory path where the visualization image will be saved
 */
void DrawOpticalFlow(const cv::Mat& cur_image_gray, const cv::Mat& next_image_gray, const double next_timestamp,
                     const std::vector<cv::Point2f>& undistorted_curr_cv_points,
                     const std::vector<cv::Point2f>& undistorted_next_cv_points,
                     const std::vector<cv::Point2f>& predicted_cv_points, const std::vector<char>& result_status,
                     const std::string& output_path) {
  cv::Mat curr_img_color;
  cv::Mat next_img_color;
  cv::cvtColor(cur_image_gray, curr_img_color, cv::COLOR_GRAY2BGR);
  cv::cvtColor(next_image_gray, next_img_color, cv::COLOR_GRAY2BGR);

  // Count tracked keypoints and draw on current frame (blue), next frame (green), and predicted motion (red)
  size_t tracked_keypoints_count{0};
  const cv::Scalar blue_color{255, 0, 0};
  const cv::Scalar green_color{0, 255, 0};
  const cv::Scalar red_color{0, 0, 255};
  const cv::Scalar orange_color{0, 165, 255};
  constexpr size_t circle_radius{3};
  constexpr int line_thickness{-1};
  const size_t keypoint_count{result_status.size()};
  for (size_t i = 0; i < keypoint_count; ++i) {
    if (result_status[i]) {
      tracked_keypoints_count++;
      cv::circle(curr_img_color, undistorted_curr_cv_points[i], circle_radius, blue_color, line_thickness);
      cv::circle(next_img_color, undistorted_next_cv_points[i], circle_radius, green_color, line_thickness);
      cv::circle(next_img_color, predicted_cv_points[i], circle_radius, red_color, line_thickness);
    }
  }

  // Add keypoint count text to images
  const std::string count_text{"Keypoints: " + std::to_string(tracked_keypoints_count)};
  const cv::Point text_position{10, 30};
  constexpr double font_scale{0.7};
  constexpr int font_thickness{2};
  cv::putText(curr_img_color, count_text, text_position, cv::FONT_HERSHEY_SIMPLEX, font_scale, orange_color,
              font_thickness);
  cv::putText(next_img_color, count_text, text_position, cv::FONT_HERSHEY_SIMPLEX, font_scale, orange_color,
              font_thickness);

  // Create side-by-side visualization
  cv::Mat visualization{};
  cv::hconcat(curr_img_color, next_img_color, visualization);

  // Display the visualization
  const std::string next_timestamp_str{std::to_string(next_timestamp)};
  const std::string filename{output_path + "/optical_flow_tracking_" + next_timestamp_str + ".png"};
  cv::imwrite(filename, visualization);
}
}  // namespace rdvio::extra